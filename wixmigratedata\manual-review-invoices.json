[{"invoice_data": {"invoice_number": "1000098", "customer_id": null, "amount": 312.5, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Frosty Solana Collective. Match: no_match (0.00)"}, "customer_name": "Frosty Solana Collective", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000097", "customer_id": null, "amount": 860.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Eat The Beat. Match: no_match (0.00)"}, "customer_name": "Eat The Beat", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000094", "customer_id": null, "amount": 95.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>-riding", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000093", "customer_id": null, "amount": 485.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Kstar Group ATF. Match: no_match (0.00)"}, "customer_name": "Kstar Group ATF", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000092", "customer_id": null, "amount": 375.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000091", "customer_id": null, "amount": 750.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000089", "customer_id": null, "amount": 350.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Eat The Beat. Match: no_match (0.00)"}, "customer_name": "Eat The Beat", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000087", "customer_id": null, "amount": 270.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000086", "customer_id": null, "amount": 275.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON> Painting Melbourne. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON> Painting Melbourne", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000084", "customer_id": null, "amount": 330.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000083", "customer_id": null, "amount": 1500.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000082", "customer_id": null, "amount": 540.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Kstar Group ATF. Match: no_match (0.00)"}, "customer_name": "Kstar Group ATF", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000081", "customer_id": null, "amount": 690.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000079", "customer_id": null, "amount": 95.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000078", "customer_id": null, "amount": 540.0, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON> Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000075", "customer_id": null, "amount": 1300.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000074", "customer_id": null, "amount": 650.0, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Ivanhoe East Primary School. Match: no_match (0.00)"}, "customer_name": "Ivanhoe East Primary School", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000072", "customer_id": null, "amount": 320.0, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <EMAIL>. Match: no_match (0.00)"}, "customer_name": "<EMAIL>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000070", "customer_id": null, "amount": 400.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000069", "customer_id": "862f339f-4836-4173-89df-133acda1ea15", "amount": 350.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: fuzzy_medium (0.71)"}, "customer_name": "<PERSON>", "match_type": "fuzzy_medium", "confidence": 0.7058823529411765, "suggested_customer_id": "862f339f-4836-4173-89df-133acda1ea15"}, {"invoice_data": {"invoice_number": "1000068", "customer_id": "2a7a2e57-dc90-44d6-99df-01bc74fd92b0", "amount": 512.5, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON>. Match: fuzzy_medium (0.74)"}, "customer_name": "<PERSON><PERSON>", "match_type": "fuzzy_medium", "confidence": 0.7407407407407407, "suggested_customer_id": "2a7a2e57-dc90-44d6-99df-01bc74fd92b0"}, {"invoice_data": {"invoice_number": "1000066", "customer_id": null, "amount": 512.5, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: La Trobe University. Match: no_match (0.00)"}, "customer_name": "La Trobe University", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000065", "customer_id": null, "amount": 320.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000063", "customer_id": null, "amount": 315.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Balloonaversal Entertainments Peter <PERSON>. Match: no_match (0.00)"}, "customer_name": "Balloonaversal Entertainments <PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000062", "customer_id": null, "amount": 580.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Officeworks Accounts Payable. Match: no_match (0.00)"}, "customer_name": "Officeworks Accounts Payable", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000060", "customer_id": null, "amount": 437.5, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: La Trobe University. Match: no_match (0.00)"}, "customer_name": "La Trobe University", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000059", "customer_id": null, "amount": 337.5, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000058", "customer_id": null, "amount": 1420.0, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000057", "customer_id": null, "amount": 250.0, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000056", "customer_id": null, "amount": 250.0, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000055", "customer_id": null, "amount": 2200.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000053", "customer_id": null, "amount": 2150.0, "currency": "AUD", "status": "void", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Dangerous Goods. Match: no_match (0.00)"}, "customer_name": "Dangerous Goods", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000050", "customer_id": null, "amount": 650.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON><PERSON> MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar. Match: no_match (0.00)"}, "customer_name": "MAGIC MOMENTS ENTERTAINMENT PTY LTD. <PERSON><PERSON><PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000049", "customer_id": null, "amount": 525.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Kstar Group ATF. Match: no_match (0.00)"}, "customer_name": "Kstar Group ATF", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000046", "customer_id": null, "amount": 190.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000043", "customer_id": null, "amount": 720.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Dangerous Goods. Match: no_match (0.00)"}, "customer_name": "Dangerous Goods", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000042", "customer_id": null, "amount": 390.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: FabFun. Match: no_match (0.00)"}, "customer_name": "FabFun", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000040", "customer_id": null, "amount": 340.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000038", "customer_id": null, "amount": 500.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: TJ Pockets. Match: no_match (0.00)"}, "customer_name": "TJ Pockets", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000037", "customer_id": null, "amount": 340.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000035", "customer_id": null, "amount": 450.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: La Trobe University. Match: no_match (0.00)"}, "customer_name": "La Trobe University", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000034", "customer_id": null, "amount": 787.5, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Eat The Beat. Match: no_match (0.00)"}, "customer_name": "Eat The Beat", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000032", "customer_id": null, "amount": 1200.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: YMCA / YES Youth Hub. Match: no_match (0.00)"}, "customer_name": "YMCA / YES Youth Hub", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000031", "customer_id": null, "amount": 525.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000026", "customer_id": "49515223-e976-46da-960a-df8178845399", "amount": 440.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: fuzzy_medium (0.75)"}, "customer_name": "<PERSON>", "match_type": "fuzzy_medium", "confidence": 0.75, "suggested_customer_id": "49515223-e976-46da-960a-df8178845399"}, {"invoice_data": {"invoice_number": "1000025", "customer_id": "49515223-e976-46da-960a-df8178845399", "amount": 345.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: fuzzy_medium (0.75)"}, "customer_name": "<PERSON>", "match_type": "fuzzy_medium", "confidence": 0.75, "suggested_customer_id": "49515223-e976-46da-960a-df8178845399"}, {"invoice_data": {"invoice_number": "1000023", "customer_id": null, "amount": 650.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Sub Club Melbourne <PERSON>. Match: no_match (0.00)"}, "customer_name": "Sub Club Melbourne <PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000022", "customer_id": null, "amount": 575.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Electric Lady Land Matt Ambler. Match: no_match (0.00)"}, "customer_name": "Electric Lady Land Matt Ambler", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000020", "customer_id": null, "amount": 320.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000019", "customer_id": null, "amount": 1400.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Eat The Beat. Match: no_match (0.00)"}, "customer_name": "Eat The Beat", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000018", "customer_id": null, "amount": 1937.5, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: MYOB. Match: no_match (0.00)"}, "customer_name": "MYOB", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000016", "customer_id": null, "amount": 920.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: YMCA / YES Youth Hub. Match: no_match (0.00)"}, "customer_name": "YMCA / YES Youth Hub", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000011", "customer_id": "a10c6310-b280-4c7c-a426-5937a38505f8", "amount": 55.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Chelsea <PERSON>. Match: fuzzy_medium (0.72)"}, "customer_name": "Chelsea <PERSON>", "match_type": "fuzzy_medium", "confidence": 0.72, "suggested_customer_id": "a10c6310-b280-4c7c-a426-5937a38505f8"}, {"invoice_data": {"invoice_number": "1000009", "customer_id": null, "amount": 500.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON> (La Trobe). Match: no_match (0.00)"}, "customer_name": "<PERSON> (La Trobe)", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000005", "customer_id": null, "amount": 500.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON> trobe. Match: no_match (0.00)"}, "customer_name": "La trobe", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "1000003", "customer_id": null, "amount": 730.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: Monbulk College Parents Club. Match: no_match (0.00)"}, "customer_name": "Monbulk College Parents Club", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "0000005", "customer_id": null, "amount": 55.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}, {"invoice_data": {"invoice_number": "0000001", "customer_id": null, "amount": 330.0, "currency": "AUD", "status": "paid", "tax_amount": 0, "notes": "Migrated from Wix. Original customer: <PERSON><PERSON>. Match: no_match (0.00)"}, "customer_name": "<PERSON><PERSON>", "match_type": "no_match", "confidence": 0.0, "suggested_customer_id": null}]