#!/usr/bin/env node

/**
 * Migration Data Verification Script
 *
 * This script verifies the data migration results by checking:
 * 1. Customer count and data quality
 * 2. Booking count and relationships
 * 3. Product count and data integrity
 * 4. Database relationships and constraints
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function verifyMigrationData() {
  console.log('🔍 Starting Migration Data Verification...\n');

  const results = {
    customers: { expected: 715, actual: 0, status: '❌' },
    bookings: { expected: 33, actual: 0, status: '❌' },
    products: { expected: 17, actual: 0, status: '❌' },
    relationships: { status: '❌', details: [] },
    dataQuality: { status: '❌', issues: [] }
  };

  try {
    // 1. Verify Customer Count
    console.log('📊 Checking customer data...');
    const { count: customerCount, error: customerError } = await supabase
      .from('customers')
      .select('*', { count: 'exact', head: true });

    if (customerError) {
      console.error('❌ Error fetching customers:', customerError.message);
    } else {
      results.customers.actual = customerCount;
      results.customers.status = customerCount >= 700 ? '✅' : '⚠️';
      console.log(`   Total customers: ${customerCount} (expected: 715)`);
    }

    // 2. Verify Booking Count
    console.log('📅 Checking booking data...');
    const { count: bookingCount, error: bookingError } = await supabase
      .from('bookings')
      .select('*', { count: 'exact', head: true });

    if (bookingError) {
      console.error('❌ Error fetching bookings:', bookingError.message);
    } else {
      results.bookings.actual = bookingCount;
      results.bookings.status = bookingCount >= 30 ? '✅' : '⚠️';
      console.log(`   Total bookings: ${bookingCount} (expected: 33)`);
    }

    // 3. Verify Product Count
    console.log('🛍️ Checking product data...');
    const { count: productCount, error: productError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (productError) {
      console.error('❌ Error fetching products:', productError.message);
    } else {
      results.products.actual = productCount;
      results.products.status = productCount >= 15 ? '✅' : '⚠️';
      console.log(`   Total products: ${productCount} (expected: 17)`);
    }

    // 4. Verify Customer-Booking Relationships
    console.log('🔗 Checking data relationships...');
    const { data: bookingsWithCustomers, error: relationError } = await supabase
      .from('bookings')
      .select(`
        id,
        customer_id,
        customers:customer_id (id, name, email)
      `)
      .limit(10);

    if (relationError) {
      console.error('❌ Error checking relationships:', relationError.message);
      results.relationships.details.push('Failed to fetch booking-customer relationships');
    } else {
      const orphanedBookings = bookingsWithCustomers.filter(b => !b.customers);
      if (orphanedBookings.length === 0) {
        results.relationships.status = '✅';
        results.relationships.details.push('All bookings have valid customer relationships');
      } else {
        results.relationships.status = '⚠️';
        results.relationships.details.push(`${orphanedBookings.length} bookings have missing customer relationships`);
      }
      console.log(`   Checked ${bookingsWithCustomers.length} booking relationships`);
    }

    // 5. Data Quality Checks
    console.log('🔍 Checking data quality...');

    // Check for customers with valid emails
    const { data: customersWithEmails, error: emailError } = await supabase
      .from('customers')
      .select('id, email')
      .not('email', 'is', null)
      .neq('email', '');

    if (!emailError && customersWithEmails) {
      const emailValidationRate = (customersWithEmails.length / results.customers.actual) * 100;
      if (emailValidationRate >= 95) {
        results.dataQuality.status = '✅';
      } else {
        results.dataQuality.issues.push(`Email validation rate: ${emailValidationRate.toFixed(1)}% (expected: >95%)`);
      }
      console.log(`   Email validation rate: ${emailValidationRate.toFixed(1)}%`);
    }

    // Check for customers with phone numbers
    const { data: customersWithPhones, error: phoneError } = await supabase
      .from('customers')
      .select('id, phone')
      .not('phone', 'is', null)
      .neq('phone', '');

    if (!phoneError && customersWithPhones) {
      const phoneValidationRate = (customersWithPhones.length / results.customers.actual) * 100;
      console.log(`   Phone validation rate: ${phoneValidationRate.toFixed(1)}%`);
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    return false;
  }

  // Print Summary
  console.log('\n📋 VERIFICATION SUMMARY');
  console.log('========================');
  console.log(`${results.customers.status} Customers: ${results.customers.actual}/${results.customers.expected}`);
  console.log(`${results.bookings.status} Bookings: ${results.bookings.actual}/${results.bookings.expected}`);
  console.log(`${results.products.status} Products: ${results.products.actual}/${results.products.expected}`);
  console.log(`${results.relationships.status} Relationships: ${results.relationships.details.join(', ')}`);
  console.log(`${results.dataQuality.status} Data Quality: ${results.dataQuality.issues.length === 0 ? 'Good' : results.dataQuality.issues.join(', ')}`);

  const overallStatus = [
    results.customers.status,
    results.bookings.status,
    results.products.status,
    results.relationships.status,
    results.dataQuality.status
  ].every(status => status === '✅') ? '✅ PASSED' : '⚠️ NEEDS ATTENTION';

  console.log(`\n🎯 Overall Status: ${overallStatus}`);

  return overallStatus.includes('PASSED');
}

// Run verification
if (require.main === module) {
  verifyMigrationData()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Verification script failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyMigrationData };
