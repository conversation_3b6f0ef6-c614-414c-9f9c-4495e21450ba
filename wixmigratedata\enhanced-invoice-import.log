2025-05-27 12:43:46,362 - ERROR - Error loading customers: Invalid leading whitespace, reserved character(s), or return character(s) in header value: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY\r'
2025-05-27 12:43:46,363 - ERROR - Failed to load customers. Aborting.
2025-05-27 12:46:44,719 - ERROR - Error loading customers: HTTPSConnectionPool(host='ndlgbcsbidyhxbpqzgqp.supabase.co%0d', port=443): Max retries exceeded with url: /rest/v1/customers?select=id,name,email (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x0000027E50EA5940>: Failed to resolve 'ndlgbcsbidyhxbpqzgqp.supabase.co%0d' ([<PERSON>rrno 11001] getaddrinfo failed)"))
2025-05-27 12:46:44,720 - ERROR - Failed to load customers. Aborting.
2025-05-27 12:49:57,207 - INFO - Loaded 715 customers for matching
2025-05-27 12:49:57,208 - INFO - Starting enhanced invoice import process...
2025-05-27 12:49:57,208 - ERROR - Failed to load invoice data: [Errno 2] No such file or directory: 'wixmigratedata/cleaned_invoices.csv'
2025-05-27 12:49:57,209 - ERROR - Invoice processing failed.
2025-05-27 12:50:56,223 - INFO - Loaded 715 customers for matching
2025-05-27 12:50:56,224 - INFO - Starting enhanced invoice import process...
2025-05-27 12:50:56,229 - INFO - Loaded 73 invoice records
2025-05-27 12:50:56,233 - INFO - Generated comprehensive import report
2025-05-27 12:50:56,236 - INFO - Enhanced invoice import completed successfully!
2025-05-27 12:51:40,926 - INFO - Loaded 715 customers for matching
2025-05-27 12:51:40,932 - INFO - Starting enhanced invoice import process...
2025-05-27 12:51:40,939 - INFO - Loaded 73 invoice records
2025-05-27 12:51:40,939 - INFO - Skipping row 0: No customer name
2025-05-27 12:51:40,939 - INFO - Skipping row 1: No customer name
2025-05-27 12:51:40,940 - INFO - Skipping row 2: No customer name
2025-05-27 12:51:40,940 - INFO - Skipping row 3: No customer name
2025-05-27 12:51:40,940 - INFO - Skipping row 4: No customer name
2025-05-27 12:51:40,940 - INFO - Skipping row 5: No customer name
2025-05-27 12:51:40,940 - INFO - Skipping row 6: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 7: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 8: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 9: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 10: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 11: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 12: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 13: No customer name
2025-05-27 12:51:40,941 - INFO - Skipping row 14: No customer name
2025-05-27 12:51:40,942 - INFO - Skipping row 15: No customer name
2025-05-27 12:51:40,942 - INFO - Skipping row 16: No customer name
2025-05-27 12:51:40,942 - INFO - Skipping row 17: No customer name
2025-05-27 12:51:40,942 - INFO - Skipping row 18: No customer name
2025-05-27 12:51:40,942 - INFO - Skipping row 19: No customer name
2025-05-27 12:51:40,942 - INFO - Skipping row 20: No customer name
2025-05-27 12:51:40,942 - INFO - Skipping row 21: No customer name
2025-05-27 12:51:40,943 - INFO - Skipping row 22: No customer name
2025-05-27 12:51:40,943 - INFO - Skipping row 23: No customer name
2025-05-27 12:51:40,943 - INFO - Skipping row 24: No customer name
2025-05-27 12:51:40,943 - INFO - Skipping row 25: No customer name
2025-05-27 12:51:40,943 - INFO - Skipping row 26: No customer name
2025-05-27 12:51:40,943 - INFO - Skipping row 27: No customer name
2025-05-27 12:51:40,943 - INFO - Skipping row 28: No customer name
2025-05-27 12:51:40,944 - INFO - Skipping row 29: No customer name
2025-05-27 12:51:40,944 - INFO - Skipping row 30: No customer name
2025-05-27 12:51:40,944 - INFO - Skipping row 31: No customer name
2025-05-27 12:51:40,944 - INFO - Skipping row 32: No customer name
2025-05-27 12:51:40,945 - INFO - Skipping row 33: No customer name
2025-05-27 12:51:40,945 - INFO - Skipping row 34: No customer name
2025-05-27 12:51:40,945 - INFO - Skipping row 35: No customer name
2025-05-27 12:51:40,945 - INFO - Skipping row 36: No customer name
2025-05-27 12:51:40,945 - INFO - Skipping row 37: No customer name
2025-05-27 12:51:40,945 - INFO - Skipping row 38: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 39: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 40: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 41: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 42: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 43: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 44: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 45: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 46: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 47: No customer name
2025-05-27 12:51:40,946 - INFO - Skipping row 48: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 49: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 50: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 51: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 52: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 53: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 54: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 55: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 56: No customer name
2025-05-27 12:51:40,947 - INFO - Skipping row 57: No customer name
2025-05-27 12:51:40,948 - INFO - Skipping row 58: No customer name
2025-05-27 12:51:40,948 - INFO - Skipping row 59: No customer name
2025-05-27 12:51:40,948 - INFO - Skipping row 60: No customer name
2025-05-27 12:51:40,948 - INFO - Skipping row 61: No customer name
2025-05-27 12:51:40,948 - INFO - Skipping row 62: No customer name
2025-05-27 12:51:40,948 - INFO - Skipping row 63: No customer name
2025-05-27 12:51:40,948 - INFO - Skipping row 64: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 65: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 66: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 67: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 68: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 69: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 70: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 71: No customer name
2025-05-27 12:51:40,949 - INFO - Skipping row 72: No customer name
2025-05-27 12:51:40,950 - INFO - Generated comprehensive import report
2025-05-27 12:51:40,953 - INFO - Enhanced invoice import completed successfully!
2025-05-27 12:52:29,319 - INFO - Loaded 715 customers for matching
2025-05-27 12:52:29,320 - INFO - Starting enhanced invoice import process...
2025-05-27 12:52:29,328 - INFO - Loaded 73 invoice records
2025-05-27 12:52:29,330 - INFO - Columns: ['="Customer', '="Invoice number', '="Order number', '="Invoice status', '="Date issued', '="Due Date', '="Currency', '="Subtotal', '="Discount', '="Taxes', '="Total']
2025-05-27 12:52:29,331 - INFO - Skipping row 0: No customer name
2025-05-27 12:52:29,331 - INFO - Skipping row 1: No customer name
2025-05-27 12:52:29,331 - INFO - Skipping row 2: No customer name
2025-05-27 12:52:29,331 - INFO - Skipping row 3: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 4: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 5: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 6: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 7: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 8: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 9: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 10: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 11: No customer name
2025-05-27 12:52:29,332 - INFO - Skipping row 12: No customer name
2025-05-27 12:52:29,333 - INFO - Skipping row 13: No customer name
2025-05-27 12:52:29,333 - INFO - Skipping row 14: No customer name
2025-05-27 12:52:29,333 - INFO - Skipping row 15: No customer name
2025-05-27 12:52:29,333 - INFO - Skipping row 16: No customer name
2025-05-27 12:52:29,333 - INFO - Skipping row 17: No customer name
2025-05-27 12:52:29,333 - INFO - Skipping row 18: No customer name
2025-05-27 12:52:29,334 - INFO - Skipping row 19: No customer name
2025-05-27 12:52:29,334 - INFO - Skipping row 20: No customer name
2025-05-27 12:52:29,334 - INFO - Skipping row 21: No customer name
2025-05-27 12:52:29,334 - INFO - Skipping row 22: No customer name
2025-05-27 12:52:29,335 - INFO - Skipping row 23: No customer name
2025-05-27 12:52:29,335 - INFO - Skipping row 24: No customer name
2025-05-27 12:52:29,336 - INFO - Skipping row 25: No customer name
2025-05-27 12:52:29,336 - INFO - Skipping row 26: No customer name
2025-05-27 12:52:29,337 - INFO - Skipping row 27: No customer name
2025-05-27 12:52:29,337 - INFO - Skipping row 28: No customer name
2025-05-27 12:52:29,337 - INFO - Skipping row 29: No customer name
2025-05-27 12:52:29,337 - INFO - Skipping row 30: No customer name
2025-05-27 12:52:29,337 - INFO - Skipping row 31: No customer name
2025-05-27 12:52:29,337 - INFO - Skipping row 32: No customer name
2025-05-27 12:52:29,337 - INFO - Skipping row 33: No customer name
2025-05-27 12:52:29,338 - INFO - Skipping row 34: No customer name
2025-05-27 12:52:29,338 - INFO - Skipping row 35: No customer name
2025-05-27 12:52:29,338 - INFO - Skipping row 36: No customer name
2025-05-27 12:52:29,338 - INFO - Skipping row 37: No customer name
2025-05-27 12:52:29,340 - INFO - Skipping row 38: No customer name
2025-05-27 12:52:29,341 - INFO - Skipping row 39: No customer name
2025-05-27 12:52:29,341 - INFO - Skipping row 40: No customer name
2025-05-27 12:52:29,342 - INFO - Skipping row 41: No customer name
2025-05-27 12:52:29,343 - INFO - Skipping row 42: No customer name
2025-05-27 12:52:29,343 - INFO - Skipping row 43: No customer name
2025-05-27 12:52:29,343 - INFO - Skipping row 44: No customer name
2025-05-27 12:52:29,344 - INFO - Skipping row 45: No customer name
2025-05-27 12:52:29,344 - INFO - Skipping row 46: No customer name
2025-05-27 12:52:29,344 - INFO - Skipping row 47: No customer name
2025-05-27 12:52:29,345 - INFO - Skipping row 48: No customer name
2025-05-27 12:52:29,345 - INFO - Skipping row 49: No customer name
2025-05-27 12:52:29,346 - INFO - Skipping row 50: No customer name
2025-05-27 12:52:29,346 - INFO - Skipping row 51: No customer name
2025-05-27 12:52:29,347 - INFO - Skipping row 52: No customer name
2025-05-27 12:52:29,347 - INFO - Skipping row 53: No customer name
2025-05-27 12:52:29,347 - INFO - Skipping row 54: No customer name
2025-05-27 12:52:29,348 - INFO - Skipping row 55: No customer name
2025-05-27 12:52:29,348 - INFO - Skipping row 56: No customer name
2025-05-27 12:52:29,348 - INFO - Skipping row 57: No customer name
2025-05-27 12:52:29,349 - INFO - Skipping row 58: No customer name
2025-05-27 12:52:29,349 - INFO - Skipping row 59: No customer name
2025-05-27 12:52:29,349 - INFO - Skipping row 60: No customer name
2025-05-27 12:52:29,350 - INFO - Skipping row 61: No customer name
2025-05-27 12:52:29,350 - INFO - Skipping row 62: No customer name
2025-05-27 12:52:29,350 - INFO - Skipping row 63: No customer name
2025-05-27 12:52:29,350 - INFO - Skipping row 64: No customer name
2025-05-27 12:52:29,351 - INFO - Skipping row 65: No customer name
2025-05-27 12:52:29,351 - INFO - Skipping row 66: No customer name
2025-05-27 12:52:29,351 - INFO - Skipping row 67: No customer name
2025-05-27 12:52:29,352 - INFO - Skipping row 68: No customer name
2025-05-27 12:52:29,352 - INFO - Skipping row 69: No customer name
2025-05-27 12:52:29,352 - INFO - Skipping row 70: No customer name
2025-05-27 12:52:29,352 - INFO - Skipping row 71: No customer name
2025-05-27 12:52:29,353 - INFO - Skipping row 72: No customer name
2025-05-27 12:52:29,354 - INFO - Generated comprehensive import report
2025-05-27 12:52:29,354 - INFO - Enhanced invoice import completed successfully!
2025-05-27 12:56:38,948 - INFO - Loaded 715 customers for matching
2025-05-27 12:56:38,950 - INFO - Starting enhanced invoice import process...
2025-05-27 12:56:38,956 - INFO - Loaded 73 invoice records
2025-05-27 12:56:38,956 - INFO - Columns: ['Customer', 'Invoice number', 'Order number', 'Invoice status', 'Date issued', 'Due Date', 'Currency', 'Subtotal', 'Discount', 'Taxes', 'Total']
2025-05-27 12:56:38,956 - INFO - Processing invoice for customer: 'Ashleigh  Noonan'
2025-05-27 12:56:39,041 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:39,043 - INFO - Processing invoice for customer: 'Frosty Solana Collective'
2025-05-27 12:56:39,079 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:56:39,099 - INFO - Processing invoice for customer: 'Housing First'
2025-05-27 12:56:39,219 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:39,220 - INFO - Processing invoice for customer: 'Alexander  Vivian-riding'
2025-05-27 12:56:39,256 - INFO - Processing invoice for customer: 'Kstar Group ATF'
2025-05-27 12:56:39,275 - INFO - Processing invoice for customer: 'Jatinder Bhatti'
2025-05-27 12:56:39,294 - INFO - Processing invoice for customer: 'Abi'
2025-05-27 12:56:39,304 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:56:39,322 - INFO - Processing invoice for customer: 'Banyule City Council'
2025-05-27 12:56:39,400 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:39,401 - INFO - Processing invoice for customer: 'Clarissa PaintNSparkles'
2025-05-27 12:56:39,434 - INFO - Processing invoice for customer: 'Antonella Face Painting Melbourne'
2025-05-27 12:56:39,462 - INFO - Processing invoice for customer: 'Techa'
2025-05-27 12:56:39,539 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:39,540 - INFO - Processing invoice for customer: 'Claire Willis'
2025-05-27 12:56:39,557 - INFO - Processing invoice for customer: 'Miriam'
2025-05-27 12:56:39,569 - INFO - Processing invoice for customer: 'Kstar Group ATF'
2025-05-27 12:56:39,599 - INFO - Processing invoice for customer: 'Rochelle'
2025-05-27 12:56:39,613 - INFO - Processing invoice for customer: 'Emma Reid'
2025-05-27 12:56:39,628 - INFO - Processing invoice for customer: 'Kenny Z'
2025-05-27 12:56:39,641 - INFO - Processing invoice for customer: 'Housing First (Ruby)'
2025-05-27 12:56:39,731 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:39,732 - INFO - Processing invoice for customer: 'Nelson Alexander'
2025-05-27 12:56:39,750 - INFO - Processing invoice for customer: 'Ivanhoe East Primary School'
2025-05-27 12:56:39,786 - INFO - Processing invoice for customer: '<EMAIL>'
2025-05-27 12:56:39,810 - INFO - Processing invoice for customer: 'Riely Saville'
2025-05-27 12:56:39,829 - INFO - Processing invoice for customer: 'Amanda'
2025-05-27 12:56:39,841 - INFO - Processing invoice for customer: 'Kristy Kumar'
2025-05-27 12:56:39,857 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:56:39,961 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:39,962 - INFO - Processing invoice for customer: 'La Trobe University'
2025-05-27 12:56:39,984 - INFO - Processing invoice for customer: 'Luke Priday'
2025-05-27 12:56:40,007 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:56:40,103 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:40,103 - INFO - Processing invoice for customer: 'Balloonaversal Entertainments Peter Patterson'
2025-05-27 12:56:40,146 - INFO - Processing invoice for customer: 'Officeworks Accounts Payable'
2025-05-27 12:56:40,176 - INFO - Processing invoice for customer: 'Banyule City Council'
2025-05-27 12:56:40,283 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:40,284 - INFO - Processing invoice for customer: 'La Trobe University'
2025-05-27 12:56:40,311 - INFO - Processing invoice for customer: 'Patricia Bowlby'
2025-05-27 12:56:40,333 - INFO - Processing invoice for customer: 'Ally Maye'
2025-05-27 12:56:40,348 - INFO - Processing invoice for customer: 'Rhys Marsh'
2025-05-27 12:56:40,363 - INFO - Processing invoice for customer: 'Rhys Marsh'
2025-05-27 12:56:40,377 - INFO - Processing invoice for customer: 'Dandy Tyler'
2025-05-27 12:56:40,398 - INFO - Processing invoice for customer: 'Housing First (Ruby)'
2025-05-27 12:56:40,531 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:40,532 - INFO - Processing invoice for customer: 'Dangerous Goods'
2025-05-27 12:56:40,551 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:56:40,681 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:40,683 - INFO - Processing invoice for customer: 'MAGIC MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar'
2025-05-27 12:56:40,728 - INFO - Processing invoice for customer: 'Kstar Group ATF'
2025-05-27 12:56:40,746 - INFO - Processing invoice for customer: 'Casey Vance'
2025-05-27 12:56:40,768 - INFO - Processing invoice for customer: 'Dangerous Goods'
2025-05-27 12:56:40,786 - INFO - Processing invoice for customer: 'FabFun'
2025-05-27 12:56:40,798 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:56:40,902 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:40,903 - INFO - Processing invoice for customer: 'Casey Vance'
2025-05-27 12:56:40,923 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:56:41,057 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:41,057 - INFO - Processing invoice for customer: 'TJ Pockets'
2025-05-27 12:56:41,075 - INFO - Processing invoice for customer: 'Rachel Coppel'
2025-05-27 12:56:41,103 - INFO - Processing invoice for customer: 'La Trobe University'
2025-05-27 12:56:41,126 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:56:41,143 - INFO - Processing invoice for customer: 'YMCA / YES Youth Hub'
2025-05-27 12:56:41,163 - INFO - Processing invoice for customer: 'Ally Maye'
2025-05-27 12:56:41,178 - INFO - Processing invoice for customer: 'Amy'
2025-05-27 12:56:41,192 - INFO - Processing invoice for customer: 'Amy'
2025-05-27 12:56:41,203 - INFO - Processing invoice for customer: 'Sub Club Melbourne Jonathan Tiatia'
2025-05-27 12:56:41,233 - INFO - Processing invoice for customer: 'Electric Lady Land Matt Ambler'
2025-05-27 12:56:41,261 - INFO - Processing invoice for customer: 'Veni May'
2025-05-27 12:56:41,275 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:56:41,293 - INFO - Processing invoice for customer: 'MYOB'
2025-05-27 12:56:41,303 - INFO - Processing invoice for customer: 'YMCA / YES Youth Hub'
2025-05-27 12:56:41,324 - INFO - Processing invoice for customer: 'Housing First (Emma)'
2025-05-27 12:56:41,436 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:41,437 - INFO - Processing invoice for customer: 'Chelsea Lahra'
2025-05-27 12:56:41,454 - INFO - Processing invoice for customer: 'Milan Mili (La Trobe)'
2025-05-27 12:56:41,483 - INFO - Processing invoice for customer: 'La trobe'
2025-05-27 12:56:41,502 - INFO - Processing invoice for customer: 'Banyule City Council'
2025-05-27 12:56:41,579 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:41,580 - INFO - Processing invoice for customer: 'Monbulk College Parents Club'
2025-05-27 12:56:41,619 - INFO - Processing invoice for customer: 'Kate Virtue & Vice'
2025-05-27 12:56:41,736 - ERROR - Failed to import invoice: {"code":"PGRST204","details":null,"hint":null,"message":"Could not find the 'customer_name_original' column of 'invoices' in the schema cache"}
2025-05-27 12:56:41,737 - INFO - Processing invoice for customer: 'Chloe Selleck'
2025-05-27 12:56:41,759 - INFO - Processing invoice for customer: 'Danni Patterson'
2025-05-27 12:56:41,793 - INFO - Saved 58 cases for manual review
2025-05-27 12:56:41,794 - INFO - Generated comprehensive import report
2025-05-27 12:56:41,794 - INFO - Enhanced invoice import completed successfully!
2025-05-27 12:57:12,822 - INFO - Loaded 715 customers for matching
2025-05-27 12:57:12,823 - INFO - Starting enhanced invoice import process...
2025-05-27 12:57:12,828 - INFO - Loaded 73 invoice records
2025-05-27 12:57:12,828 - INFO - Columns: ['Customer', 'Invoice number', 'Order number', 'Invoice status', 'Date issued', 'Due Date', 'Currency', 'Subtotal', 'Discount', 'Taxes', 'Total']
2025-05-27 12:57:12,829 - INFO - Processing invoice for customer: 'Ashleigh  Noonan'
2025-05-27 12:57:12,953 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (ea6f0b1b-e309-4e40-8717-30595eb3d594, 1000099, 8a389b6a-700a-4f26-ab45-2cadfa096c71, null, null, 357.50, 0.00, AUD, null, null, sent, Migrated from Wix. Original customer: Ashleigh  Noonan. Match: e..., 2025-05-27 02:57:10.216613+00, 2025-05-27 02:57:10.216613+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:12,954 - INFO - Processing invoice for customer: 'Frosty Solana Collective'
2025-05-27 12:57:12,979 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:57:12,997 - INFO - Processing invoice for customer: 'Housing First'
2025-05-27 12:57:13,101 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (1e78da73-2596-40fa-a103-282720c11b42, 1000096, 8203d2ba-7e54-4cb2-a43a-28146266e791, null, null, 345.00, 0.00, AUD, null, null, overdue, Migrated from Wix. Original customer: Housing First. Match: know..., 2025-05-27 02:57:10.365865+00, 2025-05-27 02:57:10.365865+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:13,102 - INFO - Processing invoice for customer: 'Alexander  Vivian-riding'
2025-05-27 12:57:13,126 - INFO - Processing invoice for customer: 'Kstar Group ATF'
2025-05-27 12:57:13,155 - INFO - Processing invoice for customer: 'Jatinder Bhatti'
2025-05-27 12:57:13,175 - INFO - Processing invoice for customer: 'Abi'
2025-05-27 12:57:13,186 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:57:13,204 - INFO - Processing invoice for customer: 'Banyule City Council'
2025-05-27 12:57:13,285 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (85d7e76f-cd80-477b-8c6f-09555ab55bf3, 1000088, d38246ef-7292-4abf-bce6-14e27e072f0d, null, null, 905.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Banyule City Council. Matc..., 2025-05-27 02:57:10.553719+00, 2025-05-27 02:57:10.553719+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:13,286 - INFO - Processing invoice for customer: 'Clarissa PaintNSparkles'
2025-05-27 12:57:13,316 - INFO - Processing invoice for customer: 'Antonella Face Painting Melbourne'
2025-05-27 12:57:13,354 - INFO - Processing invoice for customer: 'Techa'
2025-05-27 12:57:13,440 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (d076726f-b1dc-433f-bc41-f686f384b77c, 1000085, b2a91167-9a75-476a-9ce4-0d2edb82ec19, null, null, 905.00, 0.00, AUD, null, null, void, Migrated from Wix. Original customer: Techa. Match: exact_match ..., 2025-05-27 02:57:10.700954+00, 2025-05-27 02:57:10.700954+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:13,440 - INFO - Processing invoice for customer: 'Claire Willis'
2025-05-27 12:57:13,461 - INFO - Processing invoice for customer: 'Miriam'
2025-05-27 12:57:13,481 - INFO - Processing invoice for customer: 'Kstar Group ATF'
2025-05-27 12:57:13,500 - INFO - Processing invoice for customer: 'Rochelle'
2025-05-27 12:57:13,516 - INFO - Processing invoice for customer: 'Emma Reid'
2025-05-27 12:57:13,532 - INFO - Processing invoice for customer: 'Kenny Z'
2025-05-27 12:57:13,545 - INFO - Processing invoice for customer: 'Housing First (Ruby)'
2025-05-27 12:57:13,641 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (497f51be-7c3c-4a2f-8e5e-ba3f81fa31ee, 1000077, 8203d2ba-7e54-4cb2-a43a-28146266e791, null, null, 665.00, 0.00, AUD, null, null, void, Migrated from Wix. Original customer: Housing First (Ruby). Matc..., 2025-05-27 02:57:10.909828+00, 2025-05-27 02:57:10.909828+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:13,642 - INFO - Processing invoice for customer: 'Nelson Alexander'
2025-05-27 12:57:13,665 - INFO - Processing invoice for customer: 'Ivanhoe East Primary School'
2025-05-27 12:57:13,692 - INFO - Processing invoice for customer: '<EMAIL>'
2025-05-27 12:57:13,715 - INFO - Processing invoice for customer: 'Riely Saville'
2025-05-27 12:57:13,733 - INFO - Processing invoice for customer: 'Amanda'
2025-05-27 12:57:13,746 - INFO - Processing invoice for customer: 'Kristy Kumar'
2025-05-27 12:57:13,761 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:57:13,863 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (106cb8e3-2675-44ad-a3f7-b18ebc18243e, 1000067, d6525433-e649-49e8-9736-5f0dba93ec64, null, null, 2025.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Kate - Virtue Vice. Match:..., 2025-05-27 02:57:11.12992+00, 2025-05-27 02:57:11.12992+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:13,864 - INFO - Processing invoice for customer: 'La Trobe University'
2025-05-27 12:57:13,892 - INFO - Processing invoice for customer: 'Luke Priday'
2025-05-27 12:57:13,912 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:57:14,016 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (fa3413fc-cd8d-46ba-99b4-7dac06a52aec, 1000064, d6525433-e649-49e8-9736-5f0dba93ec64, null, null, 1365.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Kate - Virtue Vice. Match:..., 2025-05-27 02:57:11.276275+00, 2025-05-27 02:57:11.276275+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:14,017 - INFO - Processing invoice for customer: 'Balloonaversal Entertainments Peter Patterson'
2025-05-27 12:57:14,059 - INFO - Processing invoice for customer: 'Officeworks Accounts Payable'
2025-05-27 12:57:14,085 - INFO - Processing invoice for customer: 'Banyule City Council'
2025-05-27 12:57:14,158 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (825712cb-5417-43e9-9c91-57a81986f25d, 1000061, d38246ef-7292-4abf-bce6-14e27e072f0d, null, null, 2100.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Banyule City Council. Matc..., 2025-05-27 02:57:11.425871+00, 2025-05-27 02:57:11.425871+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:14,159 - INFO - Processing invoice for customer: 'La Trobe University'
2025-05-27 12:57:14,186 - INFO - Processing invoice for customer: 'Patricia Bowlby'
2025-05-27 12:57:14,213 - INFO - Processing invoice for customer: 'Ally Maye'
2025-05-27 12:57:14,227 - INFO - Processing invoice for customer: 'Rhys Marsh'
2025-05-27 12:57:14,242 - INFO - Processing invoice for customer: 'Rhys Marsh'
2025-05-27 12:57:14,256 - INFO - Processing invoice for customer: 'Dandy Tyler'
2025-05-27 12:57:14,272 - INFO - Processing invoice for customer: 'Housing First (Ruby)'
2025-05-27 12:57:14,383 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (764142e8-2d6e-4bba-aa72-28c94e2ad31a, 1000054, 8203d2ba-7e54-4cb2-a43a-28146266e791, null, null, 320.00, 0.00, AUD, null, null, overdue, Migrated from Wix. Original customer: Housing First (Ruby). Matc..., 2025-05-27 02:57:11.65389+00, 2025-05-27 02:57:11.65389+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:14,384 - INFO - Processing invoice for customer: 'Dangerous Goods'
2025-05-27 12:57:14,403 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:57:14,566 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (8fea30bc-57f0-47e1-817e-1af64359fee3, 1000051, d6525433-e649-49e8-9736-5f0dba93ec64, null, null, 1365.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Kate - Virtue Vice. Match:..., 2025-05-27 02:57:11.82953+00, 2025-05-27 02:57:11.82953+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:14,567 - INFO - Processing invoice for customer: 'MAGIC MOMENTS ENTERTAINMENT PTY LTD. Priyanshu Tomar'
2025-05-27 12:57:14,613 - INFO - Processing invoice for customer: 'Kstar Group ATF'
2025-05-27 12:57:14,632 - INFO - Processing invoice for customer: 'Casey Vance'
2025-05-27 12:57:14,648 - INFO - Processing invoice for customer: 'Dangerous Goods'
2025-05-27 12:57:14,666 - INFO - Processing invoice for customer: 'FabFun'
2025-05-27 12:57:14,677 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:57:14,798 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (4e07d18e-a7bb-48aa-8488-2443c8f04e15, 1000041, d6525433-e649-49e8-9736-5f0dba93ec64, null, null, 500.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Kate - Virtue Vice. Match:..., 2025-05-27 02:57:12.055379+00, 2025-05-27 02:57:12.055379+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:14,800 - INFO - Processing invoice for customer: 'Casey Vance'
2025-05-27 12:57:14,817 - INFO - Processing invoice for customer: 'Kate - Virtue Vice'
2025-05-27 12:57:14,919 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (77abaac1-6b4a-4649-8349-d3aab90e4d07, 1000039, d6525433-e649-49e8-9736-5f0dba93ec64, null, null, 665.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Kate - Virtue Vice. Match:..., 2025-05-27 02:57:12.188646+00, 2025-05-27 02:57:12.188646+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:14,920 - INFO - Processing invoice for customer: 'TJ Pockets'
2025-05-27 12:57:14,935 - INFO - Processing invoice for customer: 'Rachel Coppel'
2025-05-27 12:57:14,954 - INFO - Processing invoice for customer: 'La Trobe University'
2025-05-27 12:57:14,982 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:57:15,000 - INFO - Processing invoice for customer: 'YMCA / YES Youth Hub'
2025-05-27 12:57:15,022 - INFO - Processing invoice for customer: 'Ally Maye'
2025-05-27 12:57:15,037 - INFO - Processing invoice for customer: 'Amy'
2025-05-27 12:57:15,046 - INFO - Processing invoice for customer: 'Amy'
2025-05-27 12:57:15,056 - INFO - Processing invoice for customer: 'Sub Club Melbourne Jonathan Tiatia'
2025-05-27 12:57:15,086 - INFO - Processing invoice for customer: 'Electric Lady Land Matt Ambler'
2025-05-27 12:57:15,113 - INFO - Processing invoice for customer: 'Veni May'
2025-05-27 12:57:15,127 - INFO - Processing invoice for customer: 'Eat The Beat'
2025-05-27 12:57:15,145 - INFO - Processing invoice for customer: 'MYOB'
2025-05-27 12:57:15,154 - INFO - Processing invoice for customer: 'YMCA / YES Youth Hub'
2025-05-27 12:57:15,174 - INFO - Processing invoice for customer: 'Housing First (Emma)'
2025-05-27 12:57:15,254 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (ef3fc3cb-a91b-4d93-83b1-8c1315fd503b, 1000014, 8203d2ba-7e54-4cb2-a43a-28146266e791, null, null, 665.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Housing First (Emma). Matc..., 2025-05-27 02:57:12.520256+00, 2025-05-27 02:57:12.520256+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:15,255 - INFO - Processing invoice for customer: 'Chelsea Lahra'
2025-05-27 12:57:15,275 - INFO - Processing invoice for customer: 'Milan Mili (La Trobe)'
2025-05-27 12:57:15,308 - INFO - Processing invoice for customer: 'La trobe'
2025-05-27 12:57:15,323 - INFO - Processing invoice for customer: 'Banyule City Council'
2025-05-27 12:57:15,412 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (6058c43b-3587-49c6-bcfc-ddf222494a7d, 1000004, d38246ef-7292-4abf-bce6-14e27e072f0d, null, null, 2100.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Banyule City Council. Matc..., 2025-05-27 02:57:12.680542+00, 2025-05-27 02:57:12.680542+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:15,412 - INFO - Processing invoice for customer: 'Monbulk College Parents Club'
2025-05-27 12:57:15,442 - INFO - Processing invoice for customer: 'Kate Virtue & Vice'
2025-05-27 12:57:15,550 - ERROR - Failed to import invoice: {"code":"23502","details":"Failing row contains (b87f993e-3d89-4e89-8092-bc76ce59fa96, 1000002, d6525433-e649-49e8-9736-5f0dba93ec64, null, null, 400.00, 0.00, AUD, null, null, paid, Migrated from Wix. Original customer: Kate Virtue & Vice. Match:..., 2025-05-27 02:57:12.816181+00, 2025-05-27 02:57:12.816181+00, null, 0.00, 0.00, null, null).","hint":null,"message":"null value in column \"issue_date\" of relation \"invoices\" violates not-null constraint"}
2025-05-27 12:57:15,551 - INFO - Processing invoice for customer: 'Chloe Selleck'
2025-05-27 12:57:15,567 - INFO - Processing invoice for customer: 'Danni Patterson'
2025-05-27 12:57:15,596 - INFO - Saved 58 cases for manual review
2025-05-27 12:57:15,597 - INFO - Generated comprehensive import report
2025-05-27 12:57:15,598 - INFO - Enhanced invoice import completed successfully!
